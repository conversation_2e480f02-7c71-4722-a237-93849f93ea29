/* Leaflet地图样式 */
@import 'leaflet/dist/leaflet.css';

@import "tailwindcss";

/* 定义自定义颜色 - Tailwind CSS v4 语法 */
@theme {
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
}

/* 自定义样式 */
@layer base {
  html {
    /* 为不同语言配置合适的字体栈 */
    font-family:
      'Inter',
      /* 简体中文字体 */
      'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑',
      /* 繁体中文字体 */
      'PingFang TC', 'Hiragino Sans CNS', 'Microsoft JhengHei', '微軟正黑體',
      /* 英文和系统字体 */
      system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
      /* Emoji字体 */
      'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  }

  /* 针对不同语言的字体优化 */
  html[lang="zh-CN"] {
    font-family:
      'Inter', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑',
      system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
  }

  html[lang="zh-HK"], html[lang="zh-TW"] {
    font-family:
      'Inter', 'PingFang TC', 'Hiragino Sans CNS', 'Microsoft JhengHei', '微軟正黑體',
      system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
  }

  html[lang="en"], html[lang="en-US"] {
    font-family:
      'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      'Helvetica Neue', Arial, sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .input-field {
    @apply block w-full rounded-lg border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 text-sm;
  }

  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200;
  }

  /* 移动端优化 */
  .mobile-nav {
    @apply fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 z-40;
  }

  .mobile-nav-item {
    @apply flex flex-col items-center justify-center py-2 px-1 text-xs font-medium text-gray-600 hover:text-primary-600 transition-colors;
  }

  .mobile-nav-item.active {
    @apply text-primary-600;
  }

  /* 响应式容器 */
  .container-responsive {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* 响应式网格 */
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }

  /* 响应式文本 */
  .text-responsive-xl {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }

  .text-responsive-lg {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  /* 触摸友好的按钮 */
  .btn-touch {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  /* 模态框响应式 */
  .modal-responsive {
    @apply w-full max-w-lg mx-4 sm:mx-auto;
  }

  /* 表单响应式 */
  .form-responsive {
    @apply space-y-4 sm:space-y-6;
  }

  .form-grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6;
  }
}

/* 修复Leaflet图标问题 */
.leaflet-default-icon-path {
  background-image: url('leaflet/dist/images/marker-icon.png');
}

/* 移动端优化 */
@media (max-width: 768px) {
  /* 减少移动端的内边距 */
  .mobile-padding {
    @apply px-4 py-4;
  }

  /* 移动端全宽按钮 */
  .mobile-full-width {
    @apply w-full;
  }

  /* 移动端文本大小调整 */
  .mobile-text-sm {
    @apply text-sm;
  }

  /* 移动端卡片间距 */
  .mobile-card-spacing {
    @apply space-y-4;
  }

  /* 移动端模态框 */
  .mobile-modal {
    @apply fixed inset-x-4 top-8 bottom-8 max-h-none overflow-y-auto;
  }

  /* 移动端表单 */
  .mobile-form-spacing {
    @apply space-y-4;
  }

  /* 移动端网格调整 */
  .mobile-grid-1 {
    @apply grid-cols-1;
  }

  /* 移动端隐藏 */
  .mobile-hidden {
    @apply hidden;
  }

  /* 移动端显示 */
  .mobile-show {
    @apply block;
  }
}

/* 平板端优化 */
@media (min-width: 768px) and (max-width: 1024px) {
  .tablet-grid-2 {
    @apply grid-cols-2;
  }

  .tablet-padding {
    @apply px-6 py-6;
  }
}

/* 桌面端优化 */
@media (min-width: 1024px) {
  .desktop-grid-3 {
    @apply grid-cols-3;
  }

  .desktop-grid-4 {
    @apply grid-cols-4;
  }

  .desktop-padding {
    @apply px-8 py-8;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  /* 增加触摸目标大小 */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  /* 移除悬停效果 */
  .no-hover {
    @apply hover:bg-transparent hover:text-current;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .high-contrast {
    @apply border-2 border-black;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .reduce-motion {
    @apply transition-none;
  }

  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 暗色模式支持（预留） */
@media (prefers-color-scheme: dark) {
  .dark-mode {
    @apply bg-gray-900 text-white;
  }
}

/* 打印样式 */
@media print {
  .print-hidden {
    @apply hidden;
  }

  .print-show {
    @apply block;
  }

  body {
    @apply text-black bg-white;
  }
}
