<script setup lang="ts">
import { RouterView } from 'vue-router'
import { onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
import MobileNav from '@/components/MobileNav.vue'

const authStore = useAuthStore()
const { locale } = useI18n()

onMounted(() => {
  // 初始化认证状态
  authStore.initAuth()

  // 设置初始语言
  updateHtmlLang(locale.value)
})

// 监听语言变化，更新HTML的lang属性
watch(locale, (newLocale) => {
  updateHtmlLang(newLocale)
})

// 更新HTML的lang属性
function updateHtmlLang(localeCode: string) {
  document.documentElement.lang = localeCode
}
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 主要内容 -->
    <div class="pb-16 sm:pb-0">
      <RouterView />
    </div>

    <!-- 移动端底部导航 -->
    <MobileNav />
  </div>
</template>

<style scoped>
</style>
